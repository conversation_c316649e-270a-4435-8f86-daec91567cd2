#!/usr/bin/env python3
"""
Test script untuk memverifikasi konfigurasi Refine AF yang baru
"""

import sys
import os

# Tambahkan path ke Software directory
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from configuration import config

def test_refine_config():
    """Test konfigurasi refine AF"""
    print("="*60)
    print("TEST KONFIGURASI REFINE AUTOFOCUS")
    print("="*60)
    
    # Test parameter refine
    print("\n1. Parameter Refine AF:")
    refine_params = [
        ("range_mm", "autofocus.refine.range_mm", 0.08),
        ("feed_rate_mmpm", "autofocus.refine.feed_rate_mmpm", 0.5),
        ("direction_scan_range", "autofocus.refine.direction_scan_range", 0.04),
        ("direction_scan_speed", "autofocus.refine.direction_scan_speed", 0.3),
        ("min_frames_before_early_stop", "autofocus.refine.min_frames_before_early_stop", 20),
        ("early_stop_drop_pct", "autofocus.refine.early_stop_drop_pct", 0.15),
        ("min_decrease_window", "autofocus.refine.min_decrease_window", 8),
        ("min_z_after_peak_mm", "autofocus.refine.min_z_after_peak_mm", 0.001),
        ("patience_limit", "autofocus.refine.patience_limit", 3),
        ("direction_samples", "autofocus.refine.direction_samples", 15),
        ("stabilization_delay", "autofocus.refine.stabilization_delay", 0.1),
    ]
    
    for param_name, config_key, expected in refine_params:
        value = config.get(config_key, "NOT_FOUND")
        status = "✓" if value == expected else "✗"
        print(f"   {status} {param_name:25} = {value:8} (expected: {expected})")
    
    # Test parameter fine (untuk perbandingan)
    print("\n2. Parameter Fine AF (untuk perbandingan):")
    fine_params = [
        ("range_mm", "autofocus.fine.range_mm", 0.1),
        ("feed_rate_mmpm", "autofocus.fine.feed_rate_mmpm", 1.0),
        ("min_frames_before_early_stop", "autofocus.fine.min_frames_before_early_stop", 12),
        ("early_stop_drop_pct", "autofocus.fine.early_stop_drop_pct", 0.30),
        ("patience_limit", "autofocus.fine.patience_limit", 2),
    ]
    
    for param_name, config_key, expected in fine_params:
        value = config.get(config_key, "NOT_FOUND")
        status = "✓" if value == expected else "✗"
        print(f"   {status} {param_name:25} = {value:8} (expected: {expected})")
    
    # Test parameter coarse (untuk perbandingan)
    print("\n3. Parameter Coarse AF (untuk perbandingan):")
    coarse_params = [
        ("start_z", "autofocus.coarse.start_z", 9.0),
        ("end_z", "autofocus.coarse.end_z", 10.5),
        ("feed_rate_mmpm", "autofocus.coarse.feed_rate_mmpm", 10.0),
    ]
    
    for param_name, config_key, expected in coarse_params:
        value = config.get(config_key, "NOT_FOUND")
        status = "✓" if value == expected else "✗"
        print(f"   {status} {param_name:25} = {value:8} (expected: {expected})")
    
    # Analisis perbandingan
    print("\n4. Analisis Perbandingan:")
    print("   Mode      | Range   | Speed     | Early Stop | Patience | Min Frames")
    print("   ----------|---------|-----------|------------|----------|------------")
    
    coarse_range = config.get("autofocus.coarse.end_z", 10.5) - config.get("autofocus.coarse.start_z", 9.0)
    fine_range = config.get("autofocus.fine.range_mm", 0.1)
    refine_range = config.get("autofocus.refine.range_mm", 0.08)
    
    coarse_speed = config.get("autofocus.coarse.feed_rate_mmpm", 10.0)
    fine_speed = config.get("autofocus.fine.feed_rate_mmpm", 1.0)
    refine_speed = config.get("autofocus.refine.feed_rate_mmpm", 0.5)
    
    coarse_early = config.get("autofocus.early_stop_drop_pct", 0.10)
    fine_early = config.get("autofocus.fine.early_stop_drop_pct", 0.30)
    refine_early = config.get("autofocus.refine.early_stop_drop_pct", 0.15)
    
    coarse_patience = 2  # default
    fine_patience = config.get("autofocus.fine.patience_limit", 2)
    refine_patience = config.get("autofocus.refine.patience_limit", 3)
    
    coarse_frames = config.get("autofocus.min_samples_before_stop", 5)
    fine_frames = config.get("autofocus.fine.min_frames_before_early_stop", 12)
    refine_frames = config.get("autofocus.refine.min_frames_before_early_stop", 20)
    
    print(f"   Coarse    | {coarse_range:5.1f}mm | {coarse_speed:7.1f}mm/min | {coarse_early:8.0%} | {coarse_patience:8} | {coarse_frames:10}")
    print(f"   Fine      | {fine_range:5.2f}mm | {fine_speed:7.1f}mm/min | {fine_early:8.0%} | {fine_patience:8} | {fine_frames:10}")
    print(f"   Refine    | {refine_range:5.2f}mm | {refine_speed:7.1f}mm/min | {refine_early:8.0%} | {refine_patience:8} | {refine_frames:10}")
    
    print("\n5. Validasi Logika:")
    
    # Validasi range
    if refine_range < fine_range:
        print("   ✓ Refine range lebih kecil dari Fine (lebih presisi)")
    else:
        print("   ✗ Refine range harus lebih kecil dari Fine")
    
    # Validasi speed
    if refine_speed < fine_speed:
        print("   ✓ Refine speed lebih lambat dari Fine (lebih akurat)")
    else:
        print("   ✗ Refine speed harus lebih lambat dari Fine")
    
    # Validasi early stop
    if refine_early < fine_early:
        print("   ✓ Refine early stop lebih ketat dari Fine (lebih konservatif)")
    else:
        print("   ✗ Refine early stop harus lebih ketat dari Fine")
    
    # Validasi patience
    if refine_patience > fine_patience:
        print("   ✓ Refine patience lebih tinggi dari Fine (lebih sabar)")
    else:
        print("   ✗ Refine patience harus lebih tinggi dari Fine")
    
    # Validasi frames
    if refine_frames > fine_frames:
        print("   ✓ Refine min frames lebih banyak dari Fine (lebih stabil)")
    else:
        print("   ✗ Refine min frames harus lebih banyak dari Fine")
    
    print("\n" + "="*60)
    print("TEST SELESAI")
    print("="*60)

if __name__ == "__main__":
    test_refine_config()
