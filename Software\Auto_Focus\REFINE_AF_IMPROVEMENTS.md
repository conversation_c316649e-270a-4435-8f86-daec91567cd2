# Perbaikan Refine AutoFocus

## Ma<PERSON>ah yang Diperbaiki

### Masalah Sebelumnya:
1. **Miss AF pada Refine**: Refine AF kadang miss karena menggunakan micro-scan diskrit (7 titik) yang terlalu kasar
2. **Konfigurasi Tidak Terpisah**: Refine menggunakan parameter yang sama dengan fine scan biasa
3. **Penentuan Arah Kurang Akurat**: Micro-scan sederhana tidak cukup presisi untuk menentukan arah optimal

### Solusi yang Diimplementasikan:

## 1. Konfigurasi Terpisah untuk Refine

### Parameter Baru di `configuration.py` dan `app_config.json`:

```json
"refine": {
  "range_mm": 0.08,                    // Range scan lebih pendek untuk presisi
  "feed_rate_mmpm": 0.5,              // Kecepatan lebih lambat untuk akurasi
  "direction_scan_range": 0.04,        // Range untuk direction scan
  "direction_scan_speed": 0.3,         // Kecepatan direction scan
  "z_min": 0.0,
  "z_max": 11.0,
  "min_frames_before_early_stop": 20,  // Lebih banyak frame sebelum early stop
  "early_stop_drop_pct": 0.15,         // Drop percentage lebih ketat
  "min_decrease_window": 8,            // Window penurunan lebih besar
  "min_z_after_peak_mm": 0.001,       // Jarak minimum setelah peak lebih kecil
  "patience_limit": 3,                 // Patience limit lebih tinggi
  "direction_samples": 15,             // Jumlah sampel untuk direction scan
  "stabilization_delay": 0.1           // Delay stabilisasi
}
```

## 2. Algoritma Refine AF Baru

### Tahap 1: Direction Scan (Fine Scan Continuous)
- **Scan UP**: Fine scan continuous dari posisi awal ke atas (range kecil)
- **Scan DOWN**: Fine scan continuous dari posisi awal ke bawah (range kecil)
- **Analisis**: Bandingkan skor maksimum dan rata-rata dari kedua arah
- **Pemilihan**: Pilih arah dengan skor terbaik

### Tahap 2: Main Scan
- **Arah Optimal**: Gunakan arah yang terpilih dari direction scan
- **Fine Scan Continuous**: Lakukan fine scan dengan parameter refine yang lebih ketat
- **Parameter Ketat**: Menggunakan early stop yang lebih konservatif

## 3. Keunggulan Algoritma Baru

### Akurasi Tinggi:
- **Fine Scan Continuous**: Menggunakan continuous movement untuk direction scan, bukan diskrit
- **Sampel Lebih Banyak**: 15 sampel per arah vs 7 titik diskrit sebelumnya
- **Analisis Komprehensif**: Menggunakan max score dan average score untuk pemilihan arah

### Parameter Optimal:
- **Kecepatan Lambat**: 0.5 mm/min vs 1.0 mm/min (fine scan biasa)
- **Range Pendek**: 0.08 mm vs 0.1 mm untuk presisi tinggi
- **Early Stop Ketat**: 15% drop vs 30% (fine scan biasa)

### Stabilitas:
- **Patience Tinggi**: 3 vs 2 untuk menghindari early stop prematur
- **Frame Minimum**: 20 frame sebelum early stop vs 12 (fine scan biasa)
- **Stabilization Delay**: 0.1 detik untuk memastikan gerakan stabil

## 4. Alur Kerja Refine AF

```
1. Validasi posisi awal dan batas Z
2. Direction Scan UP (continuous fine scan)
   - Kumpulkan 15 sampel skor
   - Hitung max dan average score
3. Kembali ke posisi awal
4. Direction Scan DOWN (continuous fine scan)
   - Kumpulkan 15 sampel skor
   - Hitung max dan average score
5. Analisis dan pilih arah terbaik
6. Main Scan ke arah terpilih
   - Menggunakan parameter refine yang ketat
   - Early stop konservatif
   - Patience tinggi
7. Finalisasi di posisi fokus optimal
```

## 5. Perbedaan dengan AF Full dan Fine Scan

| Parameter | Coarse | Fine | Refine |
|-----------|--------|------|--------|
| Range | 1.5 mm | 0.1 mm | 0.08 mm |
| Speed | 10 mm/min | 1.0 mm/min | 0.5 mm/min |
| Early Stop Drop | 10% | 30% | 15% |
| Min Frames | 5 | 12 | 20 |
| Patience | 2 | 2 | 3 |
| Direction Method | N/A | N/A | Fine Scan Continuous |

## 6. Manfaat Perbaikan

### Eliminasi Miss AF:
- **Direction Scan Akurat**: Fine scan continuous memberikan data yang lebih akurat
- **Parameter Ketat**: Early stop yang lebih konservatif mencegah stop prematur
- **Sampel Cukup**: 15 sampel per arah memastikan data yang representatif

### Presisi Tinggi:
- **Kecepatan Lambat**: Memberikan waktu lebih untuk pengukuran akurat
- **Range Pendek**: Fokus pada area yang paling relevan
- **Stabilisasi**: Delay yang cukup untuk memastikan gerakan stabil

### Robustness:
- **Patience Tinggi**: Menghindari early stop karena noise sesaat
- **Validasi Batas**: Memastikan gerakan dalam batas aman
- **Error Handling**: Penanganan error yang komprehensif

## 7. Penggunaan

Refine AF dapat dipanggil melalui:
```python
autofocus_worker.run_refinement_autofocus()
```

Atau melalui UI dengan tombol "Refine AF" yang akan menggunakan algoritma baru ini.

## 8. Monitoring dan Debug

Sistem akan memberikan log detail:
- Progress direction scan
- Hasil analisis arah
- Parameter yang digunakan
- Statistik performa

Log dapat dilihat di console untuk monitoring dan debugging.
