# File: autofocus_worker.py
# Versi final yang menggabungkan Coarse-to-Fine Continuous Sweep dengan Early Stopping.

from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QTimer
from PyQt5.QtWidgets import QApplication
import cv2
import numpy as np
import time
from configuration import config
from .Calculate_AF import FocusCalculator

class AutoFocusWorker(QObject):
    """
    Worker yang ber<PERSON>lan di thread terpisah untuk melakukan proses auto focus
    tanpa membekukan antarmuka pengguna (UI).
    """
    # Sinyal untuk komunikasi kembali ke UI
    status_changed = pyqtSignal(str)
    focus_finished = pyqtSignal(float, float)
    curve_data_ready = pyqtSignal(dict, dict)
    focus_score_updated = pyqtSignal(float, float)  # (z_position, focus_score)

    def __init__(self, camera_worker, grbl_controller, parent=None):
        super().__init__(parent)
        self.camera = camera_worker
        self.grbl = grbl_controller
        self.is_running = False
        self.af_timer = None

        # Variabel untuk mengelola proses dua tahap
        self.is_in_fine_scan = False

        # Properti untuk sapuan saat ini
        self.current_z_start = 0.0
        self.current_z_end = 0.0
        self.current_speed_mmps = 0.0
        self.total_move_duration = 0.0
        self.move_start_time = 0.0

        # Properti untuk melacak fokus
        self.focus_scores = []
        self.z_positions = []
        self.best_focus_score = -1.0
        self.best_focus_z = 0.0

        # Inisialisasi Focus Calculator
        self.focus_calculator = FocusCalculator()
        print("AutoFocusWorker: FocusCalculator initialized with optimized parameters for each method")

        # Properti untuk 'patience' dan benchmarking
        self.patience_counter = 0

        # Flag untuk cleanup
        self._cleanup_done = False
        self.PATIENCE_LIMIT = 2
        # MIN_SCORE_THRESHOLD akan diambil dari method yang sedang digunakan
        self.total_calc_time = 0.0
        self.calc_count = 0
        self.calc_times = []
        self.prev_timestamp = None

        # Event-driven frame processing flags
        self._frame_events_connected = False
        self.USE_EVENT_FRAMES = bool(config.get("autofocus.use_event_frames", True))

        # Mode flag: refinement via fine scan (single-sweep)
        self._refine_mode = False

        # Dynamic fine threshold computed from coarse peak for early-stop gating (95% of coarse peak)
        self.dynamic_fine_threshold = None

        # Sinkronisasi Z dan Focus Score (soft-coded from configuration)
        self.USE_GRBL_POSITION = bool(config.get("autofocus.use_grbl_position", True))
        self.Z_SMOOTHING_ALPHA = float(config.get("autofocus.z_smoothing_alpha", 0.4))
        self._z_filtered = None

        # Smoothing skor fokus untuk kurangi noise fluktuasi frame-ke-frame
        self.SMTH_ALPHA = float(config.get("autofocus.score_smoothing_alpha", 0.3))
        self.smoothed_scores = []
        # Frame gating and stabilization parameters (to suppress start-of-scan anomalies)
        self.SKIP_INITIAL_FRAMES = int(config.get("autofocus.skip_initial_frames", 3))
        self.WARMUP_MS = float(config.get("autofocus.warmup_ms", 80.0))
        self.MIN_TRAVEL_FOR_VALID_MM = float(config.get("autofocus.min_travel_valid_mm", 0.005))
        self.STABILIZATION_SLEEP_MS = float(config.get("autofocus.stabilization_sleep_ms", 80.0))
        self._skip_frames_remaining = 0
        self._warmup_until = 0.0

        # Early stop yang lebih andal
        self.EARLY_STOP_DROP_PCT = float(config.get("autofocus.early_stop_drop_pct", 0.10))
        self.MIN_Z_AFTER_PEAK_MM = float(config.get("autofocus.min_z_after_peak_mm", 0.005))
        self.MIN_DECREASE_WINDOW = int(config.get("autofocus.min_decrease_window", 3))

        # Mode-specific early-stop hardening (fine/refine stricter; configurable)
        self.FINE_MIN_FRAMES_BEFORE_EARLY_STOP = int(config.get("autofocus.fine.min_frames_before_early_stop", 12))
        self.REFINE_MIN_FRAMES_BEFORE_EARLY_STOP = int(config.get("autofocus.refine.min_frames_before_early_stop", 20))

        self.FINE_EARLY_STOP_DROP_PCT = float(config.get("autofocus.fine.early_stop_drop_pct", 0.30))
        self.REFINE_EARLY_STOP_DROP_PCT = float(config.get("autofocus.refine.early_stop_drop_pct", 0.15))

        self.FINE_MIN_DECREASE_WINDOW = int(config.get("autofocus.fine.min_decrease_window", 6))
        self.REFINE_MIN_DECREASE_WINDOW = int(config.get("autofocus.refine.min_decrease_window", 8))

        self.FINE_MIN_Z_AFTER_PEAK_MM = float(config.get("autofocus.fine.min_z_after_peak_mm", 0.002))
        self.REFINE_MIN_Z_AFTER_PEAK_MM = float(config.get("autofocus.refine.min_z_after_peak_mm", 0.001))

        # Mode-specific patience limits (requested: similar to coarse=2)
        self.FINE_PATIENCE_LIMIT = int(config.get("autofocus.fine.patience_limit", 2))
        self.REFINE_PATIENCE_LIMIT = int(config.get("autofocus.refine.patience_limit", 3))

        # Universal patience early stop guard
        self.IMPROVEMENT_EPS_PCT = float(config.get("autofocus.improvement_eps_pct", 0.005))
        self.MIN_SAMPLES_BEFORE_STOP = int(config.get("autofocus.min_samples_before_stop", 5))

        # Stats counters initialized to avoid attribute errors across modes
        self._af_frames_count = 0
        self._cam_fps_samples = []
        
        # Konfigurasi untuk verifikasi tambahan
        self.ADDITIONAL_Z_OFFSET = float(config.get("autofocus.verify.additional_z_offset", 0.021))

        # Initialize scan containers and durations
        self.coarse_scan = {"z_positions": [], "focus_scores": [], "calc_times": []}
        self.fine_scan = {"z_positions": [], "focus_scores": [], "calc_times": []}
        self.coarse_duration = 0.0
        self.fine_duration = 0.0

        # Camera frames baseline for drop-rate stats
        self._cam_total_frames_start = None

    def __del__(self):
        """Destructor untuk memastikan cleanup yang proper"""
        self.cleanup()

    def cleanup(self):
        """
        Method untuk cleanup yang aman dari thread manapun
        Menghentikan timer dan membersihkan resources
        """
        if self._cleanup_done:
            return

        try:
            self.is_running = False

            # Hentikan timer dengan aman
            if self.af_timer is not None:
                if self.af_timer.thread() == self.thread():
                    # Timer di thread yang sama, aman untuk stop langsung
                    self.af_timer.stop()
                    self.af_timer.deleteLater()
                else:
                    # Timer di thread berbeda, gunakan QMetaObject.invokeMethod
                    from PyQt5.QtCore import QMetaObject, Qt
                    QMetaObject.invokeMethod(self.af_timer, "stop", Qt.QueuedConnection)
                    QMetaObject.invokeMethod(self.af_timer, "deleteLater", Qt.QueuedConnection)

                self.af_timer = None

            # Safely disconnect frame events if connected
            try:
                if hasattr(self, "_disconnect_frame_events"):
                    self._disconnect_frame_events()
            except Exception:
                pass

            # Restore camera RealTime option (back to drop-pending mode for UI/live)
            try:
                if hasattr(self.camera, "hcam") and self.camera.hcam:
                    self.camera.hcam.put_RealTime(1)
                    print("[AF] Camera RealTime restored to 1 (cleanup)")
            except Exception as _e:
                print(f"[AF] Failed to restore camera RealTime=1 on cleanup: {_e}")

            self._cleanup_done = True
            print("AutoFocusWorker: Cleanup completed successfully")

        except Exception as e:
            print(f"AutoFocusWorker: Error during cleanup: {e}")
            self._cleanup_done = True

    def _create_timer_safely(self):
        """
        Membuat timer dengan aman di thread yang benar
        """
        try:
            if self.af_timer is not None:
                self.af_timer.stop()
                self.af_timer.deleteLater()

            self.af_timer = QTimer()
            self.af_timer.setSingleShot(False)  # periodic timer
            self.af_timer.timeout.connect(self.measure_focus_continuous)
            self.af_timer.moveToThread(self.thread())
            print("AutoFocusWorker: Timer created successfully in correct thread (periodic)")
            return True
        except Exception as e:
            print(f"AutoFocusWorker: Error creating timer: {e}")
            return False

    def _get_current_z_position(self, elapsed_time_fallback: float) -> float:
        """
        Dapatkan posisi Z saat ini dengan prioritas:
        1) Poll GRBL (aktual), 2) Estimasi berbasis waktu (fallback).
        """
        try:
            if self.USE_GRBL_POSITION and hasattr(self.grbl, "get_current_position"):
                _, _, z = self.grbl.get_current_position()
                # Smoothing kecil agar stabil saat plotting
                if self._z_filtered is None:
                    self._z_filtered = z
                else:
                    self._z_filtered = (1 - self.Z_SMOOTHING_ALPHA) * self._z_filtered + self.Z_SMOOTHING_ALPHA * z
                return self._z_filtered
        except Exception as e:
            print(f"[WARN] Gagal polling posisi GRBL, fallback ke estimasi waktu: {e}")

        # Fallback: estimasi dari waktu dan kecepatan
        direction = np.sign(self.current_z_end - self.current_z_start)
        return self.current_z_start + (elapsed_time_fallback * self.current_speed_mmps * direction)

    def _update_smoothed_score(self, score: float) -> float:
        """
        Update EMA score untuk meredam noise; return nilai smoothed terbaru.
        """
        if not self.smoothed_scores:
            sm = score
        else:
            sm = (1 - self.SMTH_ALPHA) * self.smoothed_scores[-1] + self.SMTH_ALPHA * score
        self.smoothed_scores.append(sm)
        return sm

    def _is_last_k_decreasing(self, k: int) -> bool:
        """
        Cek apakah k skor terakhir mengalami penurunan monoton (berdasar skor smoothed).
        """
        if len(self.smoothed_scores) < k + 1:
            return False
        window = self.smoothed_scores[-(k+1):]
        return all(window[i] >= window[i+1] for i in range(len(window) - 1))

    def _calculate_focus_score(self, frame):
        """
        Menghitung focus score menggunakan Calculate_AF module
        Mengirimkan frame utuh ke FocusCalculator dan menerima score kembali
        """
        if frame is None or frame.size == 0:
            return 0.0, 0.0

        try:
            # Gunakan FocusCalculator untuk menghitung score
            score, duration = self.focus_calculator.calculate_focus_score(frame)
            return score, duration
        except Exception as e:
            print(f"Error di _calculate_focus_score: {e}")
            return 0.0, 0.0

    def set_focus_method(self, method: str):
        """
        Set method untuk perhitungan focus score
        Setiap method sudah memiliki parameter optimal built-in

        Args:
            method: Nama method ("sobel_gradient", "laplacian_variance", dll)
        """
        if self.focus_calculator:
            self.focus_calculator.set_method(method)

            # Get threshold info untuk method ini
            threshold_info = self.focus_calculator.get_method_threshold(method)
            print(f"AutoFocusWorker: Focus method changed to {method}")
            print(f"  - Good threshold: {threshold_info['good_threshold']}")
            print(f"  - Score range: {threshold_info['min_score']}-{threshold_info['max_score']}")
            print(f"  - {threshold_info['description']}")

    def get_current_threshold(self) -> float:
        """
        Mendapatkan threshold yang sesuai dengan method yang sedang digunakan

        Returns:
            float: Good threshold untuk method saat ini
        """
        if self.focus_calculator:
            threshold_info = self.focus_calculator.get_method_threshold()
            return threshold_info.get('good_threshold', 5.0)
        return 5.0


    def evaluate_current_focus(self, score: float) -> dict:
        """
        Evaluasi kualitas focus dengan method yang sedang digunakan

        Args:
            score: Focus score

        Returns:
            dict: Evaluasi kualitas focus
        """
        if self.focus_calculator:
            return self.focus_calculator.evaluate_focus_quality(score)
        return {
            'raw_score': score,
            'normalized_score': score,
            'quality': 'Unknown',
            'color': 'gray',
            'confidence': 0
        }

    def get_focus_method_info(self) -> dict:
        """
        Mendapatkan informasi method focus yang tersedia dan parameter optimal

        Returns:
            dict: Informasi method dan parameter optimal masing-masing
        """
        if self.focus_calculator:
            return self.focus_calculator.get_method_info()
        return {}

    def benchmark_focus_methods(self, frame) -> dict:
        """
        Benchmark semua method focus yang tersedia

        Args:
            frame: Frame untuk testing

        Returns:
            dict: Hasil benchmark
        """
        if self.focus_calculator and frame is not None:
            return self.focus_calculator.benchmark_methods(frame, iterations=5)
        return {}

    @pyqtSlot()
    def run_autofocus(self):
        """Metode utama yang dipanggil UI. Mengorkestrasi proses AF dua tahap."""
        if self.is_running:
            return

        self.is_running = True
        self.is_in_fine_scan = False
        self.total_calc_time, self.calc_count = 0.0, 0

        # Reset statistik FPS untuk keseluruhan sesi AF
        self._af_frames_count = 0
        self._cam_fps_samples = []

        self.status_changed.emit("--- Tahap 1: Pencarian Kasar ---")
        print("\n" + "="*50)
        print("[AF START] Memulai proses Auto Focus dua tahap...")

        self._start_continuous_sweep(
            start_z=float(config.get("autofocus.coarse.start_z", 9.0)),
            end_z=float(config.get("autofocus.coarse.end_z", 10.5)),
            feed_rate_mmpm=float(config.get("autofocus.coarse.feed_rate_mmpm", 10.0))
        )
        # Simpan baseline total frame kamera sebelum AF dimulai
        try:
            self._cam_total_frames_start = int(self.camera.get_camera_total_frames())
        except Exception:
            self._cam_total_frames_start = None

    def _start_continuous_sweep(self, start_z, end_z, feed_rate_mmpm):
        """Metode helper untuk memulai satu sapuan kontinu menggunakan estimasi waktu."""
        self.focus_scores, self.z_positions = [], []
        self.best_focus_score, self.best_focus_z = -1.0, 0.0
        self.patience_counter = 0
        self.calc_times = []

        # Reset per-sweep stats/caches
        self._af_frames_count = 0
        self._cam_fps_samples = []
        self.smoothed_scores = []
        # Reset frame gating counters for this sweep
        self._skip_frames_remaining = int(self.SKIP_INITIAL_FRAMES)
        self._warmup_until = 0.0
        # Reset Z filter to avoid cross-sweep bias
        self._z_filtered = None

        self.current_z_start = start_z
        self.current_z_end = end_z
        self.current_speed_mmps = feed_rate_mmpm / 60.0
        distance = abs(self.current_z_end - self.current_z_start)
        self.total_move_duration = distance / self.current_speed_mmps if self.current_speed_mmps > 0 else 0
        jog_direction = distance if end_z > start_z else -distance

        print(f"[AF SWEEP] Parameter: StartZ={start_z:.3f}, EndZ={end_z:.3f}, Jarak={distance:.3f} mm, Kecepatan={feed_rate_mmpm} mm/min")
        print(f"[AF SWEEP] Estimasi Durasi: ~{self.total_move_duration:.2f} detik")

        self.grbl.move_to_z(self.current_z_start)
        self.grbl.wait_for_idle()
        time.sleep(0.5)  # Tambahkan penundaan ekstra untuk memastikan Z-axis berhenti sepenuhnya

        self.status_changed.emit(f"Memulai sapuan (Durasi Maks: ~{self.total_move_duration:.2f}s)...")
        self.grbl.send_command(f"$J=G91 Z{jog_direction:.3f} F{feed_rate_mmpm}")
        # Gate incoming frames for a short warmup window after motion starts
        self._warmup_until = time.time() + (self.WARMUP_MS / 1000.0)
        self.move_start_time = time.time()

        self.phase_start_time = time.time()

        # Camera SDK: flush stale frames at sweep boundary, then enter AF mode
        try:
            if hasattr(self.camera, "hcam") and self.camera.hcam:
                # Drop any pending frames from previous sweep
                self.camera.hcam.put_RealTime(1)  # drop-pending mode
                time.sleep(self.STABILIZATION_SLEEP_MS / 1000.0)
                # Clear last cached frame
                if hasattr(self.camera, "_last_numpy_frame"):
                    self.camera._last_numpy_frame = None
                # Switch to AF mode to minimize SDK-level drops during measurement
                self.camera.hcam.put_RealTime(0)
                print("[AF] Camera RealTime flushed (1) then set to 0 for AF")
        except Exception as e:
            print(f"[AF] Failed to toggle camera RealTime for AF: {e}")

        # Event-driven vs timer-driven processing
        use_events = bool(getattr(self, "USE_EVENT_FRAMES", True))
        if use_events:
            # ensure no timer running
            if self.af_timer:
                try:
                    self.af_timer.stop()
                except Exception:
                    pass
            self._connect_frame_events()
            print("AutoFocusWorker: Using event-driven frames for AF")
        else:
            # Buat timer dengan aman
            if not self.af_timer:
                if not self._create_timer_safely():
                    print("AutoFocusWorker: Failed to create timer, aborting sweep")
                    return
            # Start timer dengan error handling
            try:
                self.af_timer.start(int(config.get("autofocus.timer_interval_ms", 50)))
                print("AutoFocusWorker: Timer started successfully")
            except Exception as e:
                print(f"AutoFocusWorker: Error starting timer: {e}")
                return

    def _process_frame_common(self, current_z: float, frame, timestamp: float) -> None:
        """
        Core processing shared by timer-loop and frame-event loop.
        Computes focus score, updates state, checks early-stop conditions.
        """
        if frame is None or not self.is_running or self._cleanup_done:
            return

        # Gate early frames to avoid spikes from direction reversal/backlog
        if self._skip_frames_remaining > 0:
            self._skip_frames_remaining -= 1
            return
        if self._warmup_until and timestamp < self._warmup_until:
            return
        # Ensure some travel has occurred from sweep start to avoid backlash transients
        if abs(current_z - self.current_z_start) < self.MIN_TRAVEL_FOR_VALID_MM:
            return

        # Hitung skor fokus dan durasi kalkulasi dalam satu langkah
        score, duration = self._calculate_focus_score(frame)
        if score < 0:
            score = 0.0

        # Simpan hasil kalkulasi
        self.total_calc_time += duration
        self.calc_count += 1
        self.focus_scores.append(score)          # raw score
        self.z_positions.append(current_z)

        # Smoothing skor untuk robust peak detection
        smoothed = self._update_smoothed_score(score)

        if 0.0001 < duration < 1.0:
            self.calc_times.append(duration)
        else:
            print(f"[WARNING] Frame {self.calc_count}: durasi aneh ({duration:.4f} detik), diabaikan.")

        # Emit signal untuk real-time focus score update (gunakan raw agar konsisten dengan UI sebelumnya)
        self.focus_score_updated.emit(current_z, score)

        # Logika pencarian fokus terbaik (gunakan smoothed untuk robustness)
        # Reset patience hanya bila ada peningkatan signifikan (epsilon) untuk menangani plateau
        improve_eps = self.best_focus_score * (1 + self.IMPROVEMENT_EPS_PCT)
        if smoothed > improve_eps:
            self.best_focus_score = smoothed
            self.best_focus_z = current_z
            self.patience_counter = 0
        else:
            self.patience_counter += 1

        # Kirim update status ke UI
        self.status_changed.emit(
            f"Skor: {score:.2f} (smth {smoothed:.2f}) di Z≈{current_z:.3f} (Patience: {self.patience_counter})"
        )

        # Early stopping yang lebih cepat dan tepat
        current_threshold = self.get_current_threshold()
        drop_from_peak = (self.best_focus_score - smoothed) / max(self.best_focus_score, 1e-6)
        passed_peak_distance = abs(current_z - self.best_focus_z)

        # Mode-based parameters
        if not self.is_in_fine_scan:
            # Coarse
            dec_window_req = self.MIN_DECREASE_WINDOW
            drop_pct_req = self.EARLY_STOP_DROP_PCT
            min_dz_req = self.MIN_Z_AFTER_PEAK_MM
            min_frames_es = max(5, self.MIN_SAMPLES_BEFORE_STOP)
            mode_patience_limit = self.PATIENCE_LIMIT
        elif self._refine_mode:
            # Refine (strict)
            dec_window_req = self.REFINE_MIN_DECREASE_WINDOW
            drop_pct_req = self.REFINE_EARLY_STOP_DROP_PCT
            min_dz_req = self.REFINE_MIN_Z_AFTER_PEAK_MM
            min_frames_es = self.REFINE_MIN_FRAMES_BEFORE_EARLY_STOP
            mode_patience_limit = self.REFINE_PATIENCE_LIMIT
        else:
            # Fine (strict)
            dec_window_req = self.FINE_MIN_DECREASE_WINDOW
            drop_pct_req = self.FINE_EARLY_STOP_DROP_PCT
            min_dz_req = self.FINE_MIN_Z_AFTER_PEAK_MM
            min_frames_es = self.FINE_MIN_FRAMES_BEFORE_EARLY_STOP
            mode_patience_limit = self.FINE_PATIENCE_LIMIT

        # Threshold usage per mode:
        # - Coarse: use method threshold (good_threshold from current method)
        # - Fine  : require reaching 95% of coarse peak (dynamic_fine_threshold), fallback to method threshold if None
        # - Refine: no threshold (unchanged)
        if not self.is_in_fine_scan:
            use_threshold = True
            threshold_to_use = current_threshold
        elif self._refine_mode:
            use_threshold = False
            threshold_to_use = None
        else:
            use_threshold = True
            threshold_to_use = self.dynamic_fine_threshold if self.dynamic_fine_threshold is not None else current_threshold

        # Jangan early-stop terlalu cepat: wajib kumpulkan sejumlah frame minimum (mode-dependent)
        if len(self.focus_scores) < min_frames_es:
            # Skip early-stop and patience fallback until enough frames collected
            pass
        else:
            early_stop_conditions_met = (
                self._is_last_k_decreasing(dec_window_req) and
                drop_from_peak >= drop_pct_req and
                passed_peak_distance >= min_dz_req
            )
            if use_threshold:
                early_stop_conditions_met = early_stop_conditions_met and (self.best_focus_score >= (threshold_to_use if threshold_to_use is not None else current_threshold))

            if early_stop_conditions_met:
                if use_threshold:
                    thr = threshold_to_use if threshold_to_use is not None else current_threshold
                    print(f"[AF SWEEP] Early stop: peak={self.best_focus_score:.2f} (> thr {thr:.2f}), "
                          f"drop={drop_from_peak*100:.1f}%, dz={passed_peak_distance:.3f} mm. Menghentikan sapuan lebih awal.")
                else:
                    print(f"[AF SWEEP] Early stop (no threshold in refine): peak={self.best_focus_score:.2f}, "
                          f"drop={drop_from_peak*100:.1f}%, dz={passed_peak_distance:.3f} mm.")
                self.finish_sweep()
                return

            # Patience fallback per-mode:
            # - Wajib kumpulkan minimal frame sesuai mode (min_frames_es)
            # - Coarse: butuh threshold terpenuhi
            # - Fine/Refine: tanpa threshold, tetap butuh jarak setelah puncak untuk stabilitas
            if (len(self.focus_scores) >= min_frames_es and
                self.patience_counter >= mode_patience_limit and
                ((self.best_focus_score >= (threshold_to_use if threshold_to_use is not None else current_threshold)) if use_threshold else True) and
                passed_peak_distance >= min_dz_req):
                if use_threshold:
                    thr = threshold_to_use if threshold_to_use is not None else current_threshold
                    print(f"[AF SWEEP] Patience limit tercapai ({mode_patience_limit}). Peak={self.best_focus_score:.2f} >= thr {thr:.2f}. Early stop.")
                else:
                    print(f"[AF SWEEP] Patience limit tercapai ({mode_patience_limit}) [refine, no threshold]. Early stop.")
                self.finish_sweep()
                return

    @pyqtSlot(object)
    def _on_frame_event(self, frame):
        """
        Frame-event driven path: called whenever Main_Camera emits numpy_frame_ready.
        This reduces drop rate by processing every delivered frame instead of polling by QTimer.
        """
        if not self.is_running or self._cleanup_done:
            return

        elapsed_time = time.time() - self.move_start_time
        if elapsed_time >= self.total_move_duration:
            print("[AF SWEEP] Sapuan selesai karena durasi maksimal tercapai (event loop).")
            self.finish_sweep()
            return

        # Hitung posisi Z saat ini selama sapuan
        current_z = self._get_current_z_position(elapsed_time_fallback=elapsed_time)

        # Akumulasi statistik FPS untuk laporan akhir sesi AF
        try:
            cam_fps_now = float(self.camera.get_camera_fps())
            if cam_fps_now > 0:
                self._cam_fps_samples.append(cam_fps_now)
        except Exception:
            pass
        self._af_frames_count += 1

        timestamp = time.time()
        self.prev_timestamp = timestamp

        self._process_frame_common(current_z, frame, timestamp)

    def _connect_frame_events(self):
        """Connect to camera numpy_frame_ready once per sweep if configured."""
        if not getattr(self, "_frame_events_connected", False) and hasattr(self.camera, "numpy_frame_ready"):
            try:
                self.camera.numpy_frame_ready.connect(self._on_frame_event)
                self._frame_events_connected = True
                print("AutoFocusWorker: Connected to camera numpy_frame_ready (event-driven AF)")
            except Exception as e:
                print(f"AutoFocusWorker: Failed to connect frame events: {e}")

    def _disconnect_frame_events(self):
        """Safely disconnect frame event to avoid leaks or duplicated processing."""
        if getattr(self, "_frame_events_connected", False) and hasattr(self.camera, "numpy_frame_ready"):
            try:
                self.camera.numpy_frame_ready.disconnect(self._on_frame_event)
            except Exception:
                # ignore if already disconnected
                pass
            self._frame_events_connected = False
            print("AutoFocusWorker: Disconnected from camera numpy_frame_ready")

    def measure_focus_continuous(self):
        """Dipanggil oleh QTimer untuk mengukur fokus selama gerakan kontinu."""
        if not self.is_running or self._cleanup_done:
            if self.af_timer:
                try:
                    self.af_timer.stop()
                except Exception as e:
                    print(f"AutoFocusWorker: Error stopping timer in measure_focus_continuous: {e}")
            return

        elapsed_time = time.time() - self.move_start_time
        if elapsed_time >= self.total_move_duration:
            print("[AF SWEEP] Sapuan selesai karena durasi maksimal tercapai.")
            self.finish_sweep()
            return

        # Hitung posisi Z saat ini selama sapuan (sinkronisasi lebih baik dengan polling GRBL)
        current_z = self._get_current_z_position(elapsed_time_fallback=elapsed_time)
        # Akumulasi statistik FPS untuk laporan akhir sesi AF
        try:
            cam_fps_now = float(self.camera.get_camera_fps())
            if cam_fps_now > 0:
                self._cam_fps_samples.append(cam_fps_now)
        except Exception:
            pass
        self._af_frames_count += 1

        # Ambil frame terbaru dari kamera
        frame = self.camera.get_latest_numpy_frame()
        timestamp = time.time()
        # simpan timestamp untuk debug (delta tidak digunakan)
        self.prev_timestamp = timestamp
        # Early-frame gating to avoid anomalies at sweep start
        if (self._skip_frames_remaining > 0 or
            (self._warmup_until and timestamp < self._warmup_until) or
            abs(current_z - self.current_z_start) < self.MIN_TRAVEL_FOR_VALID_MM):
            if self._skip_frames_remaining > 0:
                self._skip_frames_remaining -= 1
            return

        if frame is not None:
            # Hitung skor fokus dan durasi kalkulasi dalam satu langkah
            score, duration = self._calculate_focus_score(frame)

            # Simpan hasil kalkulasi
            self.total_calc_time += duration
            self.calc_count += 1
            self.focus_scores.append(score)          # raw score
            self.z_positions.append(current_z)

            # Smoothing skor untuk robust peak detection
            smoothed = self._update_smoothed_score(score)

            if 0.0001 < duration < 1.0:
                self.calc_times.append(duration)
            else:
                print(f"[WARNING] Frame {self.calc_count}: durasi aneh ({duration:.4f} detik), diabaikan.")

            # Emit signal untuk real-time focus score update (gunakan raw agar konsisten dengan UI sebelumnya)
            self.focus_score_updated.emit(current_z, score)

            # Logika pencarian fokus terbaik (gunakan smoothed untuk robustness)
            # Reset patience hanya bila ada peningkatan signifikan (epsilon) untuk menangani plateau
            improve_eps = self.best_focus_score * (1 + self.IMPROVEMENT_EPS_PCT)
            if smoothed > improve_eps:
                self.best_focus_score = smoothed
                self.best_focus_z = current_z
                self.patience_counter = 0
            else:
                self.patience_counter += 1

            # Kirim update status ke UI
            self.status_changed.emit(
                f"Skor: {score:.2f} (smth {smoothed:.2f}) di Z≈{current_z:.3f} (Patience: {self.patience_counter})"
            )

            # Early stopping yang lebih cepat dan tepat
            current_threshold = self.get_current_threshold()
            drop_from_peak = (self.best_focus_score - smoothed) / max(self.best_focus_score, 1e-6)
            passed_peak_distance = abs(current_z - self.best_focus_z)

            # Mode-based parameters
            if not self.is_in_fine_scan:
                # Coarse
                dec_window_req = self.MIN_DECREASE_WINDOW
                drop_pct_req = self.EARLY_STOP_DROP_PCT
                min_dz_req = self.MIN_Z_AFTER_PEAK_MM
                min_frames_es = max(5, self.MIN_SAMPLES_BEFORE_STOP)
                mode_patience_limit = self.PATIENCE_LIMIT
            elif self._refine_mode:
                # Refine (strict)
                dec_window_req = self.REFINE_MIN_DECREASE_WINDOW
                drop_pct_req = self.REFINE_EARLY_STOP_DROP_PCT
                min_dz_req = self.REFINE_MIN_Z_AFTER_PEAK_MM
                min_frames_es = self.REFINE_MIN_FRAMES_BEFORE_EARLY_STOP
                mode_patience_limit = self.REFINE_PATIENCE_LIMIT
            else:
                # Fine (strict)
                dec_window_req = self.FINE_MIN_DECREASE_WINDOW
                drop_pct_req = self.FINE_EARLY_STOP_DROP_PCT
                min_dz_req = self.FINE_MIN_Z_AFTER_PEAK_MM
                min_frames_es = self.FINE_MIN_FRAMES_BEFORE_EARLY_STOP
                mode_patience_limit = self.FINE_PATIENCE_LIMIT

            # Threshold usage per mode:
            # - Coarse: use method threshold (good_threshold from current method)
            # - Fine  : require reaching 95% of coarse peak (dynamic_fine_threshold), fallback to method threshold if None
            # - Refine: no threshold (unchanged)
            if not self.is_in_fine_scan:
                use_threshold = True
                threshold_to_use = current_threshold
            elif self._refine_mode:
                use_threshold = False
                threshold_to_use = None
            else:
                use_threshold = True
                threshold_to_use = self.dynamic_fine_threshold if self.dynamic_fine_threshold is not None else current_threshold

            # Jangan early-stop terlalu cepat: wajib kumpulkan sejumlah frame minimum (mode-dependent)
            if len(self.focus_scores) < min_frames_es:
                # Skip early-stop and patience fallback until enough frames collected
                pass
            else:
                early_stop_conditions_met = (
                    self._is_last_k_decreasing(dec_window_req) and
                    drop_from_peak >= drop_pct_req and
                    passed_peak_distance >= min_dz_req
                )
                if use_threshold:
                    early_stop_conditions_met = early_stop_conditions_met and (self.best_focus_score >= (threshold_to_use if threshold_to_use is not None else current_threshold))

                if early_stop_conditions_met:
                    if use_threshold:
                        thr = threshold_to_use if threshold_to_use is not None else current_threshold
                        print(f"[AF SWEEP] Early stop: peak={self.best_focus_score:.2f} (> thr {thr:.2f}), "
                              f"drop={drop_from_peak*100:.1f}%, dz={passed_peak_distance:.3f} mm. Menghentikan sapuan lebih awal.")
                    else:
                        print(f"[AF SWEEP] Early stop (no threshold in refine): peak={self.best_focus_score:.2f}, "
                              f"drop={drop_from_peak*100:.1f}%, dz={passed_peak_distance:.3f} mm.")
                    self.finish_sweep()
                    return

                # Patience fallback per-mode:
                if (len(self.focus_scores) >= min_frames_es and
                    self.patience_counter >= mode_patience_limit and
                    ((self.best_focus_score >= (threshold_to_use if threshold_to_use is not None else current_threshold)) if use_threshold else True) and
                    passed_peak_distance >= min_dz_req):
                    if use_threshold:
                        thr = threshold_to_use if threshold_to_use is not None else current_threshold
                        print(f"[AF SWEEP] Patience limit tercapai ({mode_patience_limit}). Peak={self.best_focus_score:.2f} >= thr {thr:.2f}. Early stop.")
                    else:
                        print(f"[AF SWEEP] Patience limit tercapai ({mode_patience_limit}) [refine, no threshold]. Early stop.")
                    self.finish_sweep()
                    return


    def finish_sweep(self):
        """Router yang dipanggil setelah setiap sapuan selesai."""
        # Hentikan timer dengan aman
        if self.af_timer:
            try:
                self.af_timer.stop()
            except Exception as e:
                print(f"AutoFocusWorker: Error stopping timer in finish_sweep: {e}")

        # Hentikan gerakan GRBL dengan aman
        try:
            self.grbl.send_command("\x85\n")
            time.sleep(0.1)
            self.grbl.send_command("~")
            time.sleep(0.1)
        except Exception as e:
            print(f"AutoFocusWorker: Error stopping GRBL in finish_sweep: {e}")

        self.phase_end_time = time.time()
        phase_duration = self.phase_end_time - self.phase_start_time

        if not self.is_in_fine_scan:
            self.coarse_duration = phase_duration
        else:
            self.fine_duration = phase_duration

        if not self.is_in_fine_scan:
            self.status_changed.emit(f"Hasil Kasar: Z={self.best_focus_z:.3f}. Memulai Pencarian Halus...")
            print(f"[AF TRANSITION] Hasil Kasar: Z={self.best_focus_z:.3f}. Memulai Pencarian Halus...")
            self.coarse_scan = {
                "z_start": self.current_z_start,
                "z_positions": self.z_positions,
                "focus_scores": self.focus_scores,
                "calc_times": self.calc_times
                }

            # Hitung threshold Fine: 95% dari puncak coarse (dipakai untuk early-stop gating pada Fine)
            self.dynamic_fine_threshold = max(0.0, float(self.best_focus_score) * 0.95)
            print(f"[AF TRANSITION] Fine threshold set to 95% of coarse peak: {self.dynamic_fine_threshold:.2f}")
            try:
                self.status_changed.emit(f"Fine threshold: {self.dynamic_fine_threshold:.2f} (95% dari peak coarse)")
            except Exception:
                pass

            self.is_in_fine_scan = True

            _, _, last_pos_after_coarse = self.grbl.get_current_position()
            fine_range = float(config.get("autofocus.fine.range_mm", 0.1))
            fine_end_z = self.best_focus_z - fine_range if last_pos_after_coarse > self.best_focus_z else self.best_focus_z + fine_range

            self._start_continuous_sweep(
                start_z=last_pos_after_coarse,
                end_z=fine_end_z,
                feed_rate_mmpm=float(config.get("autofocus.fine.feed_rate_mmpm", 1.0))
            )

        else:
            self.status_changed.emit(f"Fokus final ditemukan di Z={self.best_focus_z:.3f}. Bergerak ke sana...")
            print(f"[AF FINISH] Fokus final ditemukan di Z={self.best_focus_z:.4f}")
            self.fine_scan = {
                "z_positions": self.z_positions,
                "focus_scores": self.focus_scores,
                "calc_times": self.calc_times
                }

            self.finish_process(self.best_focus_z, self.best_focus_score)

    def finish_process(self, final_pos, final_score):
        self.is_running = False

        # Disconnect frame events if used
        try:
            self._disconnect_frame_events()
        except Exception:
            pass

        # Cleanup timer sebelum finish
        if self.af_timer:
            try:
                self.af_timer.stop()
                self.af_timer = None
            except Exception as e:
                print(f"AutoFocusWorker: Error cleaning up timer in finish_process: {e}")

        # Restore camera RealTime option
        try:
            if hasattr(self.camera, "hcam") and self.camera.hcam:
                self.camera.hcam.put_RealTime(1)
                print("[AF] Camera RealTime restored to 1")
        except Exception as e:
            print(f"[AF] Failed to restore camera RealTime=1: {e}")

        # Pindah ke posisi final
        self.grbl.move_to_z(final_pos)
        self.grbl.wait_for_idle()

        # Verifikasi skor fokus setelah pindah ke posisi final
        print("\n" + "-"*50)
        print("[AF VERIFY] Verifikasi Skor pada Posisi Z Final...")
        time.sleep(0.2)  # Tunggu stabilisasi
        
        verification_frame = self.camera.get_latest_numpy_frame()
        if verification_frame is not None:
            verify_score, verify_time = self._calculate_focus_score(verification_frame)
            print(f"  -> Posisi Z Final Aktual : {final_pos:.4f}")
            print(f"  -> Skor Fokus Ditemukan   : {final_score:.2f} (selama sapuan)")
            print(f"  -> Skor Fokus Verifikasi  : {verify_score:.2f} (setelah idle)")
        else:
            print("  -> Gagal mendapatkan frame untuk verifikasi.")

        # Pengujian tambahan dengan menggeser Z sedikit (hanya untuk mode AF standar, bukan refine)
        if not self._refine_mode:
            print("\n[AF VERIFY] Verifikasi Tambahan dengan Offset Z...")
            additional_z_pos = final_pos + self.ADDITIONAL_Z_OFFSET
            print(f"  -> Bergerak ke Z Tambahan: {additional_z_pos:.4f} (Offset: {self.ADDITIONAL_Z_OFFSET} mm)")
            self.grbl.move_to_z(additional_z_pos)
            self.grbl.wait_for_idle()
            time.sleep(0.2)

            additional_frame = self.camera.get_latest_numpy_frame()
            if additional_frame is not None:
                additional_score, _ = self._calculate_focus_score(additional_frame)
                print(f"  -> Posisi Z Tambahan      : {additional_z_pos:.4f}")
                print(f"  -> Skor Fokus Tambahan    : {additional_score:.2f}")
            else:
                print("  -> Gagal mendapatkan frame untuk verifikasi tambahan.")
        else:
            print("\n[AF REFINE] Melewati verifikasi offset Z tambahan untuk mode refine.")

        print("-" * 50)
        print("[AF STATS] Laporan Performa Akhir:")

        # Siapkan scan data dengan aman (agar robust untuk refine single-scan)
        coarse_scan = getattr(self, "coarse_scan", {"z_positions": [], "focus_scores": [], "calc_times": []})
        fine_scan = getattr(self, "fine_scan", {"z_positions": [], "focus_scores": [], "calc_times": []})

        coarse_count = len(coarse_scan.get("focus_scores", []))
        fine_count = len(fine_scan.get("focus_scores", []))

        # Coarse stats (opsional)
        if coarse_count > 0 and getattr(self, "coarse_duration", 0) > 0:
            print(f"  -> [Coarse] Frame       : {coarse_count} frame")
            print(f"     Durasi               : {self.coarse_duration:.2f} detik")
            fps1 = coarse_count / max(self.coarse_duration, 1e-6)
            print(f"     fps                  : {fps1:.2f} fps")
        else:
            print("  -> [Coarse] Tidak ada data (skip)")

        # Fine stats (opsional)
        if fine_count > 0 and getattr(self, "fine_duration", 0) > 0:
            print(f"  -> [Fine]   Frame       : {fine_count} frame")
            print(f"     Durasi               : {self.fine_duration:.2f} detik")
            fps = fine_count / max(self.fine_duration, 1e-6)
            print(f"     fps                  : {fps:.2f} fps")
        else:
            print("  -> [Fine]   Tidak ada data (skip)")

        total_duration = 0.0
        if getattr(self, "coarse_duration", 0) > 0:
            total_duration += self.coarse_duration
        # Total frame kamera yang masuk selama AF (delta dari baseline) + Drop Rate
        cam_total_delta = None
        try:
            if getattr(self, "_cam_total_frames_start", None) is not None:
                cam_total_end = int(self.camera.get_camera_total_frames())
                cam_total_delta = max(0, cam_total_end - int(self._cam_total_frames_start))
                print(f"  -> Total Frame Kamera    : {cam_total_delta} frame")
        except Exception:
            cam_total_delta = None

        # Drop Rate: persentase frame kamera yang tidak diproses AF
        if cam_total_delta is not None and cam_total_delta > 0:
            processed = int(self.calc_count)
            dropped = max(0, cam_total_delta - processed)
            drop_pct = (dropped / cam_total_delta) * 100.0
            proc_pct = (processed / cam_total_delta) * 100.0
            print(f"  -> Drop Rate             : {drop_pct:.1f}% (processed {proc_pct:.1f}%: {processed}/{cam_total_delta})")

        if getattr(self, "fine_duration", 0) > 0:
            total_duration += self.fine_duration

        print(f"  -> Total Frame          : {self.calc_count} frame")
        if total_duration > 0:
            print(f"  -> Total Durasi         : {total_duration:.2f} detik")
        else:
            print("  -> Total Durasi         : [tidak tersedia]")

        # Ringkasan FPS loop AF (rata-rata keseluruhan sesi)
        try:
            avg_af_loop_fps = self.calc_count / max(total_duration, 1e-6) if total_duration > 0 else 0.0
        except Exception:
            avg_af_loop_fps = 0.0

        print(f"  -> FPS AF Loop (avg)    : {avg_af_loop_fps:.1f} fps")
        print("-" * 50)

        print(f"     Total Durasi         : {total_duration:.2f} detik")

        if self.calc_times:
            times_ms = [t * 1000 for t in self.calc_times]
            avg_time = np.mean(times_ms)
            min_time = np.min(times_ms)
            max_time = np.max(times_ms)
            print(f"  -> Waktu per Frame      : Rata-rata {avg_time:.2f} ms, Min {min_time:.2f} ms, Max {max_time:.2f} ms")
        else:
            print("  -> Waktu per Frame      : [tidak valid atau kosong]")

        print(f"  -> Posisi Fokus Akhir   : Z = {final_pos:.4f} (Skor: {final_score:.2f})")
        print("-" * 50)

        # Kembalikan GRBL ke thread UI dan emit sinyal
        self.grbl.moveToThread(QApplication.instance().thread())
        self.focus_finished.emit(final_pos, final_score)
        self.curve_data_ready.emit(coarse_scan, fine_scan)

        # Reset refine mode bila aktif
        self._refine_mode = False

    @pyqtSlot()
    def stop_autofocus(self):
        """Slot untuk menghentikan proses dari luar jika diperlukan."""
        print("AutoFocusWorker: Stop autofocus requested")
        self.is_running = False

        # Hentikan timer dengan aman
        if self.af_timer is not None:
            try:
                self.af_timer.stop()
                print("AutoFocusWorker: Timer stopped successfully")
            except Exception as e:
                print(f"AutoFocusWorker: Error stopping timer: {e}")

        # Putus koneksi event frame jika ada
        try:
            self._disconnect_frame_events()
        except Exception:
            pass

        # Kembalikan RealTime kamera
        try:
            if hasattr(self.camera, "hcam") and self.camera.hcam:
                self.camera.hcam.put_RealTime(1)
                print("[AF] Camera RealTime restored to 1 (stop)")
        except Exception as e:
            print(f"[AF] Failed to restore camera RealTime=1 on stop: {e}")

        # Hentikan gerakan GRBL
        try:
            self.grbl.send_command("!")
        except Exception as e:
            print(f"AutoFocusWorker: Error stopping GRBL: {e}")

        self.status_changed.emit("Auto Focus dihentikan paksa.")

    @pyqtSlot()
    def run_refinement_autofocus(self):
        """
        Refine AF dengan Fine Scan Continuous untuk penentuan arah yang akurat.
        Menggunakan dua tahap fine scan:
        1. Direction scan: fine scan pendek ke atas dan bawah untuk menentukan arah terbaik
        2. Main scan: fine scan continuous ke arah yang terpilih
        """
        if self.is_running:
            return

        self.is_running = True
        self._refine_mode = True
        self.is_in_fine_scan = True  # Perlakukan sebagai 'fine' agar finish_sweep langsung finalize
        # Tidak ada threshold pada refine mode
        self.dynamic_fine_threshold = None

        self.status_changed.emit("--- Memulai AF Penyempurnaan (Fine Scan Continuous) ---")
        print("\n" + "="*50)
        print("[AF REFINE] Memulai refine AF dengan fine scan continuous untuk penentuan arah...")

        # Parameter refine scan (menggunakan konfigurasi terpisah)
        Z_LIMIT_MIN = float(config.get("autofocus.refine.z_min", 0.0))
        Z_LIMIT_MAX = float(config.get("autofocus.refine.z_max", 11.0))
        direction_scan_range = float(config.get("autofocus.refine.direction_scan_range", 0.04))
        direction_scan_speed = float(config.get("autofocus.refine.direction_scan_speed", 0.3))
        main_scan_range = float(config.get("autofocus.refine.range_mm", 0.08))
        main_scan_speed = float(config.get("autofocus.refine.feed_rate_mmpm", 0.5))
        direction_samples = int(config.get("autofocus.refine.direction_samples", 15))
        stabilization_delay = float(config.get("autofocus.refine.stabilization_delay", 0.1))

        # Pastikan idle dan ambil posisi awal
        self.grbl.wait_for_idle()
        _, _, z0 = self.grbl.get_current_position()
        print(f"[AF REFINE] Posisi awal: Z={z0:.4f}")

        # Validasi batas Z
        if z0 < Z_LIMIT_MIN or z0 > Z_LIMIT_MAX:
            print(f"[AF REFINE] ERROR: Posisi Z={z0:.4f} di luar batas ({Z_LIMIT_MIN}-{Z_LIMIT_MAX})")
            self.status_changed.emit("ERROR: Posisi Z di luar batas aman")
            self.is_running = False
            self._refine_mode = False
            return

        # === TAHAP 1: Direction Scan (Fine Scan Continuous) ===
        print("[AF REFINE] === TAHAP 1: Direction Scan ===")
        self.status_changed.emit("Tahap 1: Menentukan arah optimal dengan fine scan...")

        direction_results = {}

        # Scan ke arah UP (positif)
        print(f"[AF REFINE] Direction scan UP: Z{z0:.4f} -> Z{z0 + direction_scan_range:.4f}")
        up_scores = self._perform_direction_scan(z0, z0 + direction_scan_range,
                                               direction_scan_speed, direction_samples,
                                               stabilization_delay, "UP")
        direction_results['up'] = {
            'scores': up_scores,
            'max_score': max(up_scores) if up_scores else 0.0,
            'avg_score': np.mean(up_scores) if up_scores else 0.0
        }

        # Kembali ke posisi awal
        self.grbl.move_to_z(z0)
        self.grbl.wait_for_idle()
        time.sleep(stabilization_delay)

        # Scan ke arah DOWN (negatif)
        print(f"[AF REFINE] Direction scan DOWN: Z{z0:.4f} -> Z{z0 - direction_scan_range:.4f}")
        down_scores = self._perform_direction_scan(z0, z0 - direction_scan_range,
                                                 direction_scan_speed, direction_samples,
                                                 stabilization_delay, "DOWN")
        direction_results['down'] = {
            'scores': down_scores,
            'max_score': max(down_scores) if down_scores else 0.0,
            'avg_score': np.mean(down_scores) if down_scores else 0.0
        }

        # Analisis hasil direction scan
        up_max = direction_results['up']['max_score']
        down_max = direction_results['down']['max_score']
        up_avg = direction_results['up']['avg_score']
        down_avg = direction_results['down']['avg_score']

        print(f"[AF REFINE] Direction scan results:")
        print(f"  UP   - Max: {up_max:.2f}, Avg: {up_avg:.2f}")
        print(f"  DOWN - Max: {down_max:.2f}, Avg: {down_avg:.2f}")

        # Pilih arah berdasarkan skor maksimum dan rata-rata
        if up_max > down_max or (up_max == down_max and up_avg > down_avg):
            chosen_direction = 1  # UP
            direction_name = "UP"
            best_max_score = up_max
        else:
            chosen_direction = -1  # DOWN
            direction_name = "DOWN"
            best_max_score = down_max

        print(f"[AF REFINE] Arah terpilih: {direction_name} (max score: {best_max_score:.2f})")
        self.status_changed.emit(f"Arah optimal: {direction_name} (score: {best_max_score:.2f})")

        # Kembali ke posisi awal untuk main scan
        self.grbl.move_to_z(z0)
        self.grbl.wait_for_idle()
        time.sleep(stabilization_delay)

        # === TAHAP 2: Main Scan ===
        print("[AF REFINE] === TAHAP 2: Main Scan ===")
        self.status_changed.emit(f"Tahap 2: Fine scan utama ke arah {direction_name}...")

        # Hitung posisi akhir main scan
        end_z = z0 + (chosen_direction * main_scan_range)

        # Validasi batas untuk main scan
        if end_z < Z_LIMIT_MIN or end_z > Z_LIMIT_MAX:
            # Sesuaikan range jika melebihi batas
            if chosen_direction > 0:
                end_z = min(Z_LIMIT_MAX, z0 + main_scan_range)
            else:
                end_z = max(Z_LIMIT_MIN, z0 - main_scan_range)
            print(f"[AF REFINE] Range disesuaikan karena batas Z: {end_z:.4f}")

        print(f"[AF REFINE] Main scan: Z{z0:.4f} -> Z{end_z:.4f} (range: {abs(end_z-z0):.4f}mm)")

        # Reset statistik untuk main scan
        self.total_calc_time, self.calc_count = 0.0, 0
        self._af_frames_count = 0
        self._cam_fps_samples = []

        # Simpan baseline total frame kamera sebelum main scan
        try:
            self._cam_total_frames_start = int(self.camera.get_camera_total_frames())
        except Exception:
            self._cam_total_frames_start = None

        # Jalankan main scan dengan parameter refine
        self._start_continuous_sweep(start_z=z0, end_z=end_z, feed_rate_mmpm=main_scan_speed)

    def _perform_direction_scan(self, start_z, end_z, feed_rate_mmpm, num_samples, stabilization_delay, direction_name):
        """
        Melakukan fine scan continuous untuk satu arah dan mengumpulkan sampel skor.

        Args:
            start_z: Posisi Z awal
            end_z: Posisi Z akhir
            feed_rate_mmpm: Kecepatan scan (mm/min)
            num_samples: Jumlah sampel yang diinginkan
            stabilization_delay: Delay stabilisasi (detik)
            direction_name: Nama arah untuk logging

        Returns:
            List skor fokus yang dikumpulkan
        """
        scores = []

        # Hitung parameter gerakan
        distance = abs(end_z - start_z)
        speed_mmps = feed_rate_mmpm / 60.0
        total_duration = distance / speed_mmps if speed_mmps > 0 else 0
        sample_interval = total_duration / num_samples if num_samples > 0 else 0.1

        print(f"[AF REFINE] {direction_name} scan: durasi={total_duration:.2f}s, interval={sample_interval:.3f}s")

        # Mulai gerakan continuous
        jog_direction = distance if end_z > start_z else -distance
        self.grbl.send_command(f"$J=G91 Z{jog_direction:.4f} F{feed_rate_mmpm}")

        # Tunggu stabilisasi awal
        time.sleep(stabilization_delay)

        start_time = time.time()
        last_sample_time = start_time

        # Kumpulkan sampel selama gerakan
        while time.time() - start_time < total_duration and len(scores) < num_samples:
            current_time = time.time()

            # Cek apakah sudah waktunya untuk sampel berikutnya
            if current_time - last_sample_time >= sample_interval:
                frame = self.camera.get_latest_numpy_frame()
                if frame is not None:
                    score, _ = self._calculate_focus_score(frame)
                    scores.append(score)

                    # Log progress
                    elapsed = current_time - start_time
                    progress = (elapsed / total_duration) * 100
                    print(f"[AF REFINE] {direction_name} sample {len(scores)}/{num_samples}: score={score:.2f} ({progress:.1f}%)")

                last_sample_time = current_time

            # Yield untuk mencegah blocking
            QApplication.processEvents()
            time.sleep(0.01)

        # Hentikan gerakan
        try:
            self.grbl.send_command("\x85\n")  # Jog cancel
            time.sleep(0.05)
            self.grbl.send_command("~")       # Cycle start/resume
            time.sleep(0.05)
        except Exception as e:
            print(f"[AF REFINE] Error stopping {direction_name} scan: {e}")

        # Tunggu idle
        self.grbl.wait_for_idle()

        print(f"[AF REFINE] {direction_name} scan selesai: {len(scores)} sampel dikumpulkan")
        return scores
